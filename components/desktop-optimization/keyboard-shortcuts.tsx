"use client"

import { useEffect, useState } from 'react'
import { useMobile } from '@/hooks/use-mobile'
import { toast } from '@/components/ui/use-toast'

interface KeyboardShortcutsProps {
  locale: string
  onFontSelect: (fontIndex: number) => void
  availableFonts: Array<{ name: string; value: string }>
}

export function KeyboardShortcuts({ locale, onFontSelect, availableFonts }: KeyboardShortcutsProps) {
  const isMobile = useMobile()
  const [showTooltip, setShowTooltip] = useState(false)
  
  useEffect(() => {
    // 只在桌面端英文用户中启用
    const isDesktop = !isMobile
    const isEnglish = locale === 'en'
    
    if (!isDesktop || !isEnglish) return
    
    // 3秒后显示功能提示
    const tooltipTimer = setTimeout(() => {
      setShowTooltip(true)
      toast({
        title: "⌨️ Keyboard Shortcuts Available",
        description: "Press 1-9 to quickly select fonts. Try it now!",
        duration: 4000,
      })
      
      // 5秒后隐藏提示
      setTimeout(() => setShowTooltip(false), 5000)
    }, 3000)
    
    const handleKeyDown = (event: KeyboardEvent) => {
      // 检查是否按下了1-9数字键
      if (/^[1-9]$/.test(event.key)) {
        const fontIndex = parseInt(event.key) - 1
        
        // 确保索引在有效范围内
        if (fontIndex < availableFonts.length) {
          event.preventDefault()
          
          // 调用字体选择回调
          onFontSelect(fontIndex)
          
          // 显示选择反馈
          const selectedFont = availableFonts[fontIndex]
          toast({
            title: `🎨 Font Selected: ${selectedFont.name}`,
            description: `Keyboard shortcut: ${event.key}`,
            duration: 2000,
          })
          
          // 发送键盘快捷键使用事件
          if (typeof window !== 'undefined' && (window as any).trackCalligraphyEvent) {
            (window as any).trackCalligraphyEvent('Desktop_Keyboard_Shortcut_Used', {
              key_pressed: event.key,
              font_index: fontIndex,
              font_name: selectedFont.name,
              timestamp: new Date().toISOString()
            })
            
            // 同时发送增强版的Font_Selected事件
            (window as any).trackCalligraphyEvent('Font_Selected', {
              step: '2_font_selection',
              font_name: selectedFont.name,
              font_category: 'Unknown', // 可以后续添加字体分类
              selection_method: 'keyboard_shortcut',
              shortcut_key: event.key,
              is_favorite: false
            })
          }
        }
      }
    }
    
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown)
    
    // 清理函数
    return () => {
      clearTimeout(tooltipTimer)
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [isMobile, locale, onFontSelect, availableFonts])
  
  // 这个组件不渲染任何UI
  return null
}
